<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Rapidgator Files Browser</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 30px; }
        table { border-collapse: collapse; width: 100%; margin-top: 20px; }
        th, td { border: 1px solid #ddd; padding: 8px; }
        th { background: #f2f2f2; cursor: pointer; }
        tr:nth-child(even) { background: #f9f9f9; }
        tr:hover { background: #e9e9e9; }
        #search { width: 300px; padding: 8px; }
    </style>
</head>
<body>
    <h1>Rapidgator Files Browser</h1>
    <input type="text" id="search" placeholder="Search files...">
    <div style="margin:10px 0;">
        <button id="prevPage">&lt; Prev</button>
        <span id="pageInfo"></span>
        <button id="nextPage">Next &gt;</button>
    </div>
    <table id="filesTable">
        <thead>
            <tr>
                <th data-sort="name">Name</th>
                <th data-sort="size">Size</th>
                <th>URL</th>
            </tr>
        </thead>
        <tbody></tbody>
    </table>
    <script>
    let files = [];
    let sortColumn = 'name';
    let sortDirection = 'asc';
    let currentPage = 1;
    const pageSize = 100;

    // Fetch the JSON file
    fetch('rapidgator_files.json')
        .then(res => res.json())
        .then(data => {
            files = data;
            renderTable();
        });

    // Render table rows with pagination
    function renderTable() {
        let search = document.getElementById('search').value.toLowerCase();
        let filtered = files.filter(f => f.name.toLowerCase().includes(search));
        filtered.sort((a, b) => {
            let valA = a[sortColumn] || '';
            let valB = b[sortColumn] || '';
            if (sortColumn === 'size') {
                return (parseSize(valA) - parseSize(valB)) * (sortDirection === 'asc' ? 1 : -1);
            } else {
                if (valA < valB) return sortDirection === 'asc' ? -1 : 1;
                if (valA > valB) return sortDirection === 'asc' ? 1 : -1;
                return 0;
            }
        });
        // Pagination
        let totalPages = Math.ceil(filtered.length / pageSize) || 1;
        if (currentPage > totalPages) currentPage = totalPages;
        let start = (currentPage - 1) * pageSize;
        let end = start + pageSize;
        let pageData = filtered.slice(start, end);
        let tbody = document.querySelector('#filesTable tbody');
        tbody.innerHTML = '';
        pageData.forEach(f => {
            let row = `<tr><td>${escapeHtml(f.name)}</td><td>${escapeHtml(f.size)}</td><td><a href="${f.url}" target="_blank">Link</a></td></tr>`;
            tbody.innerHTML += row;
        });
        document.getElementById('pageInfo').textContent = `Page ${currentPage} of ${totalPages}`;
        document.getElementById('prevPage').disabled = currentPage === 1;
        document.getElementById('nextPage').disabled = currentPage === totalPages;
    }

    // Size string to bytes
    function parseSize(sizeStr) {
        if (!sizeStr) return 0;
        let match = sizeStr.match(/([\d.]+)\s*(B|KB|MB|GB|TB)/i);
        if (!match) return 0;
        let num = parseFloat(match[1]);
        let unit = match[2].toUpperCase();
        let mult = {B:1, KB:1024, MB:1048576, GB:1073741824, TB:1099511627776};
        return num * (mult[unit] || 1);
    }

    // Escape HTML
    function escapeHtml(text) {
        return text.replace(/[&<>"']/g, function(m) {
            return ({'&':'&amp;','<':'&lt;','>':'&gt;','"':'&quot;','\'':'&#39;'})[m];
        });
    }

    // Search event
    document.getElementById('search').addEventListener('input', function() {
        currentPage = 1;
        renderTable();
    });

    document.getElementById('prevPage').addEventListener('click', function() {
        if (currentPage > 1) {
            currentPage--;
            renderTable();
        }
    });
    document.getElementById('nextPage').addEventListener('click', function() {
        let search = document.getElementById('search').value.toLowerCase();
        let filtered = files.filter(f => f.name.toLowerCase().includes(search));
        let totalPages = Math.ceil(filtered.length / pageSize) || 1;
        if (currentPage < totalPages) {
            currentPage++;
            renderTable();
        }
    });

    // Sorting event
    document.querySelectorAll('#filesTable th[data-sort]').forEach(th => {
        th.addEventListener('click', function() {
            let col = th.getAttribute('data-sort');
            if (sortColumn === col) {
                sortDirection = sortDirection === 'asc' ? 'desc' : 'asc';
            } else {
                sortColumn = col;
                sortDirection = 'asc';
            }
            currentPage = 1;
            renderTable();
        });
    });
    </script>
</body>
</html>
