<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Rapidgator Files Browser</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-color: #2563eb;
            --primary-hover: #1d4ed8;
            --secondary-color: #64748b;
            --success-color: #059669;
            --warning-color: #d97706;
            --error-color: #dc2626;
            --background-color: #f8fafc;
            --surface-color: #ffffff;
            --surface-hover: #f1f5f9;
            --text-primary: #1e293b;
            --text-secondary: #64748b;
            --text-muted: #94a3b8;
            --border-color: #e2e8f0;
            --border-hover: #cbd5e1;
            --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
            --shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1);
            --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1);
            --radius: 8px;
            --radius-lg: 12px;
            --transition: all 0.2s ease-in-out;
        }

        * {
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            background: var(--background-color);
            color: var(--text-primary);
            margin: 0;
            padding: 20px;
            line-height: 1.6;
            font-size: 14px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: var(--surface-color);
            border-radius: var(--radius-lg);
            box-shadow: var(--shadow-lg);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
            color: white;
            padding: 2rem;
            text-align: center;
        }

        .header h1 {
            margin: 0 0 0.5rem 0;
            font-size: 2rem;
            font-weight: 700;
        }

        .header .subtitle {
            margin: 0;
            opacity: 0.9;
            font-size: 1rem;
        }

        .controls {
            padding: 1.5rem;
            border-bottom: 1px solid var(--border-color);
            background: var(--surface-color);
        }

        .search-container {
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        .search-row {
            display: flex;
            gap: 1rem;
            flex-wrap: wrap;
            align-items: center;
        }

        .search-input {
            flex: 1;
            min-width: 300px;
            padding: 0.75rem 1rem;
            border: 2px solid var(--border-color);
            border-radius: var(--radius);
            font-size: 14px;
            transition: var(--transition);
            background: var(--surface-color);
        }

        .search-input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgb(37 99 235 / 0.1);
        }

        .filter-select {
            padding: 0.75rem 1rem;
            border: 2px solid var(--border-color);
            border-radius: var(--radius);
            background: var(--surface-color);
            font-size: 14px;
            cursor: pointer;
            transition: var(--transition);
            min-width: 150px;
        }

        .filter-select:focus {
            outline: none;
            border-color: var(--primary-color);
        }

        .view-controls {
            display: flex;
            gap: 0.5rem;
            margin-left: auto;
        }

        .view-btn {
            padding: 0.75rem;
            border: 2px solid var(--border-color);
            background: var(--surface-color);
            border-radius: var(--radius);
            cursor: pointer;
            transition: var(--transition);
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .view-btn:hover {
            border-color: var(--border-hover);
            background: var(--surface-hover);
        }

        .view-btn.active {
            border-color: var(--primary-color);
            background: var(--primary-color);
            color: white;
        }

        .search-stats {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 13px;
            color: var(--text-secondary);
            flex-wrap: wrap;
            gap: 1rem;
        }

        .stats-info {
            display: flex;
            gap: 2rem;
        }

        .keyboard-shortcuts {
            font-size: 12px;
            color: var(--text-muted);
        }

        .kbd {
            background: var(--surface-hover);
            border: 1px solid var(--border-color);
            border-radius: 4px;
            padding: 2px 6px;
            font-family: monospace;
            font-size: 11px;
        }

        .sr-only {
            position: absolute;
            width: 1px;
            height: 1px;
            padding: 0;
            margin: -1px;
            overflow: hidden;
            clip: rect(0, 0, 0, 0);
            white-space: nowrap;
            border: 0;
        }

        .loading-container {
            display: none;
            text-align: center;
            padding: 3rem;
        }

        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 4px solid var(--border-color);
            border-top: 4px solid var(--primary-color);
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 1rem;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .message {
            padding: 1rem;
            border-radius: var(--radius);
            margin: 1rem;
            display: none;
        }

        .message.success {
            background: rgb(220 252 231);
            color: var(--success-color);
            border: 1px solid rgb(187 247 208);
        }

        .message.error {
            background: rgb(254 242 242);
            color: var(--error-color);
            border: 1px solid rgb(252 165 165);
        }

        .table-container {
            overflow-x: auto;
            border: 1px solid var(--border-color);
            border-radius: var(--radius);
            margin: 1.5rem;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            background: var(--surface-color);
        }

        th, td {
            padding: 0.75rem 1rem;
            text-align: left;
            border-bottom: 1px solid var(--border-color);
        }

        th {
            background: var(--surface-hover);
            font-weight: 600;
            font-size: 13px;
            color: var(--text-secondary);
            text-transform: uppercase;
            letter-spacing: 0.05em;
            position: sticky;
            top: 0;
            z-index: 10;
        }

        th.sortable {
            cursor: pointer;
            user-select: none;
            transition: var(--transition);
            position: relative;
        }

        th.sortable:hover {
            background: var(--border-color);
        }

        th.sortable:focus {
            outline: 2px solid var(--primary-color);
            outline-offset: -2px;
        }

        .sort-indicator {
            margin-left: 0.5rem;
            font-size: 12px;
            opacity: 0.5;
        }

        th[aria-sort="ascending"] .sort-indicator::before {
            content: "↑";
            opacity: 1;
        }

        th[aria-sort="descending"] .sort-indicator::before {
            content: "↓";
            opacity: 1;
        }

        .checkbox-column {
            width: 40px;
            text-align: center;
        }

        .icon-column {
            width: 40px;
            text-align: center;
        }

        .size-column {
            width: 100px;
        }

        .actions-column {
            width: 120px;
        }

        tbody tr {
            transition: var(--transition);
        }

        tbody tr:hover {
            background: var(--surface-hover);
        }

        tbody tr.selected {
            background: rgb(239 246 255);
            border-color: var(--primary-color);
        }

        .file-icon {
            font-size: 16px;
            display: inline-block;
        }

        .file-name {
            font-weight: 500;
            color: var(--text-primary);
            word-break: break-word;
        }

        .file-size {
            font-family: 'SF Mono', Monaco, 'Cascadia Code', monospace;
            font-size: 13px;
            color: var(--text-secondary);
        }

        .file-link {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.5rem 1rem;
            background: var(--primary-color);
            color: white;
            text-decoration: none;
            border-radius: var(--radius);
            font-size: 13px;
            font-weight: 500;
            transition: var(--transition);
        }

        .file-link:hover {
            background: var(--primary-hover);
            transform: translateY(-1px);
        }

        .file-link:focus {
            outline: 2px solid var(--primary-color);
            outline-offset: 2px;
        }

        /* Card View Styles */
        .cards-container {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 1rem;
            padding: 1.5rem;
        }

        .file-card {
            background: var(--surface-color);
            border: 1px solid var(--border-color);
            border-radius: var(--radius);
            padding: 1rem;
            transition: var(--transition);
            cursor: pointer;
        }

        .file-card:hover {
            border-color: var(--border-hover);
            box-shadow: var(--shadow);
            transform: translateY(-2px);
        }

        .file-card.selected {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgb(37 99 235 / 0.1);
        }

        .card-header {
            display: flex;
            align-items: flex-start;
            gap: 0.75rem;
            margin-bottom: 0.75rem;
        }

        .card-icon {
            font-size: 24px;
            flex-shrink: 0;
        }

        .card-info {
            flex: 1;
            min-width: 0;
        }

        .card-name {
            font-weight: 600;
            color: var(--text-primary);
            margin: 0 0 0.25rem 0;
            word-break: break-word;
            line-height: 1.4;
        }

        .card-size {
            font-size: 13px;
            color: var(--text-secondary);
            font-family: 'SF Mono', Monaco, 'Cascadia Code', monospace;
        }

        .card-actions {
            display: flex;
            gap: 0.5rem;
            margin-top: 0.75rem;
        }

        .card-checkbox {
            margin-left: auto;
        }

        /* Pagination Styles */
        .pagination-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1.5rem;
            border-top: 1px solid var(--border-color);
            background: var(--surface-color);
            flex-wrap: wrap;
            gap: 1rem;
        }

        .pagination {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .page-btn {
            padding: 0.5rem 0.75rem;
            border: 1px solid var(--border-color);
            background: var(--surface-color);
            color: var(--text-primary);
            border-radius: var(--radius);
            cursor: pointer;
            transition: var(--transition);
            font-size: 14px;
            display: flex;
            align-items: center;
            justify-content: center;
            min-width: 40px;
        }

        .page-btn:hover:not(:disabled) {
            border-color: var(--border-hover);
            background: var(--surface-hover);
        }

        .page-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .page-btn.active {
            background: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }

        .page-info {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin: 0 1rem;
            font-size: 14px;
            color: var(--text-secondary);
        }

        .page-jump {
            width: 60px;
            padding: 0.25rem 0.5rem;
            border: 1px solid var(--border-color);
            border-radius: var(--radius);
            text-align: center;
            font-size: 14px;
        }

        .page-size-selector {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 14px;
            color: var(--text-secondary);
        }

        .page-size-selector select {
            padding: 0.5rem;
            border: 1px solid var(--border-color);
            border-radius: var(--radius);
            background: var(--surface-color);
            font-size: 14px;
        }

        /* Bulk Actions */
        .bulk-actions {
            position: fixed;
            bottom: 2rem;
            left: 50%;
            transform: translateX(-50%);
            background: var(--surface-color);
            border: 1px solid var(--border-color);
            border-radius: var(--radius-lg);
            box-shadow: var(--shadow-lg);
            z-index: 1000;
            animation: slideUp 0.3s ease-out;
        }

        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateX(-50%) translateY(100%);
            }
            to {
                opacity: 1;
                transform: translateX(-50%) translateY(0);
            }
        }

        .bulk-actions-content {
            display: flex;
            align-items: center;
            gap: 1rem;
            padding: 1rem 1.5rem;
        }

        .selection-count {
            font-weight: 600;
            color: var(--text-primary);
        }

        .bulk-buttons {
            display: flex;
            gap: 0.5rem;
        }

        .btn {
            padding: 0.5rem 1rem;
            border: 1px solid var(--border-color);
            border-radius: var(--radius);
            cursor: pointer;
            transition: var(--transition);
            font-size: 14px;
            font-weight: 500;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }

        .btn-primary {
            background: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }

        .btn-primary:hover {
            background: var(--primary-hover);
        }

        .btn-secondary {
            background: var(--surface-color);
            color: var(--text-primary);
        }

        .btn-secondary:hover {
            background: var(--surface-hover);
            border-color: var(--border-hover);
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            body {
                padding: 10px;
            }

            .header {
                padding: 1.5rem 1rem;
            }

            .header h1 {
                font-size: 1.5rem;
            }

            .search-row {
                flex-direction: column;
                align-items: stretch;
            }

            .search-input {
                min-width: auto;
            }

            .view-controls {
                margin-left: 0;
                justify-content: center;
            }

            .table-container {
                margin: 1rem;
            }

            .cards-container {
                grid-template-columns: 1fr;
                padding: 1rem;
            }

            .pagination-container {
                flex-direction: column;
                align-items: stretch;
                text-align: center;
            }

            .bulk-actions {
                left: 1rem;
                right: 1rem;
                transform: none;
            }

            .bulk-actions-content {
                flex-direction: column;
                gap: 0.75rem;
            }
        }

        /* Dark mode support */
        @media (prefers-color-scheme: dark) {
            :root {
                --background-color: #0f172a;
                --surface-color: #1e293b;
                --surface-hover: #334155;
                --text-primary: #f1f5f9;
                --text-secondary: #cbd5e1;
                --text-muted: #64748b;
                --border-color: #334155;
                --border-hover: #475569;
            }
        }
    </style>
</head>
<body>
    <main role="main" class="container">
        <header class="header">
            <h1 id="page-title">Rapidgator Files Browser</h1>
            <p class="subtitle">Browse and search through your file collections</p>
        </header>

        <div class="loading-container" id="loadingContainer">
            <div class="loading-spinner"></div>
            <p>Loading files...</p>
        </div>

        <div class="message success" id="successMessage"></div>
        <div class="message error" id="errorMessage"></div>

        <section class="controls" aria-labelledby="search-heading">
            <h2 id="search-heading" class="sr-only">Search and Filter Controls</h2>
            <div class="search-container">
                <div class="search-row">
                    <label for="search" class="sr-only">Search files by name</label>
                    <input
                        type="text"
                        id="search"
                        placeholder="Search files by name..."
                        aria-describedby="search-help"
                        class="search-input"
                        autocomplete="off"
                    >

                    <select id="sizeFilter" class="filter-select" aria-label="Filter by file size">
                        <option value="">All Sizes</option>
                        <option value="small">< 100MB</option>
                        <option value="medium">100MB - 1GB</option>
                        <option value="large">1GB - 5GB</option>
                        <option value="xlarge">> 5GB</option>
                    </select>

                    <select id="typeFilter" class="filter-select" aria-label="Filter by file type">
                        <option value="">All Types</option>
                        <option value="video">Video Files</option>
                        <option value="archive">Archives</option>
                        <option value="document">Documents</option>
                        <option value="image">Images</option>
                        <option value="audio">Audio</option>
                    </select>

                    <div class="view-controls">
                        <button
                            id="tableView"
                            class="view-btn active"
                            aria-pressed="true"
                            title="Table view"
                            aria-label="Switch to table view"
                        >
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M3 3h18v18H3V3zm2 2v14h14V5H5zm2 2h10v2H7V7zm0 4h10v2H7v-2zm0 4h10v2H7v-2z"/>
                            </svg>
                        </button>
                        <button
                            id="cardView"
                            class="view-btn"
                            aria-pressed="false"
                            title="Card view"
                            aria-label="Switch to card view"
                        >
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M3 3h8v8H3V3zm10 0h8v8h-8V3zM3 13h8v8H3v-8zm10 0h8v8h-8v-8z"/>
                            </svg>
                        </button>
                    </div>
                </div>

                <div class="search-stats">
                    <div class="stats-info">
                        <span id="searchStats">Loading...</span>
                        <span id="selectionStats" style="display: none;"></span>
                    </div>
                    <div class="keyboard-shortcuts">
                        <span class="kbd">Ctrl+F</span> Search •
                        <span class="kbd">←→</span> Navigate •
                        <span class="kbd">Esc</span> Clear
                    </div>
                </div>

                <div id="search-help" class="sr-only">
                    Type to search files by name. Use arrow keys to navigate results.
                    Press Escape to clear search.
                </div>
            </div>
        </section>

        <section class="table-section" aria-labelledby="results-heading" id="tableSection">
            <h2 id="results-heading" class="sr-only">File Results</h2>
            <div class="table-container">
                <table
                    id="filesTable"
                    role="table"
                    aria-label="File listing with sortable columns"
                    aria-describedby="table-help"
                >
                    <caption class="sr-only">
                        List of files with name, size, and download links.
                        Click column headers to sort. Use checkboxes to select multiple files.
                    </caption>
                    <thead>
                        <tr role="row">
                            <th class="checkbox-column">
                                <input
                                    type="checkbox"
                                    id="selectAll"
                                    aria-label="Select all visible files"
                                    title="Select all visible files"
                                >
                            </th>
                            <th class="icon-column" aria-label="File type"></th>
                            <th
                                role="columnheader"
                                data-sort="name"
                                aria-sort="ascending"
                                tabindex="0"
                                aria-label="File name, sortable"
                                class="sortable"
                            >
                                Name
                                <span class="sort-indicator" aria-hidden="true">↑</span>
                            </th>
                            <th
                                role="columnheader"
                                data-sort="size"
                                tabindex="0"
                                aria-label="File size, sortable"
                                class="sortable size-column"
                            >
                                Size
                                <span class="sort-indicator" aria-hidden="true"></span>
                            </th>
                            <th class="actions-column">Actions</th>
                        </tr>
                    </thead>
                    <tbody id="tableBody"></tbody>
                </table>

                <div id="table-help" class="sr-only">
                    Use arrow keys to navigate between pages.
                    Click column headers to sort by that column.
                </div>
            </div>
        </section>

        <section class="card-section" aria-labelledby="results-heading" id="cardSection" style="display: none;">
            <div class="cards-container" id="cardsContainer"></div>
        </section>

        <nav class="pagination-container" aria-label="File listing pagination">
            <div class="pagination" id="pagination"></div>
            <div class="page-size-selector">
                <label for="pageSize">Items per page:</label>
                <select id="pageSize" aria-label="Number of items per page">
                    <option value="25">25</option>
                    <option value="50">50</option>
                    <option value="100" selected>100</option>
                    <option value="200">200</option>
                    <option value="500">500</option>
                </select>
            </div>
        </nav>

        <div class="bulk-actions" id="bulkActions" style="display: none;">
            <div class="bulk-actions-content">
                <span class="selection-count" id="selectionCount"></span>
                <div class="bulk-buttons">
                    <button class="btn btn-secondary" id="copyUrls">Copy URLs</button>
                    <button class="btn btn-secondary" id="exportSelected">Export Selected</button>
                    <button class="btn btn-secondary" id="clearSelection">Clear Selection</button>
                </div>
            </div>
        </div>
    </main>
    <script>
    // Global state
    let files = [];
    let filteredFiles = [];
    let sortColumn = 'name';
    let sortDirection = 'asc';
    let currentPage = 1;
    let pageSize = 100;
    let currentView = 'table';
    let selectedFiles = new Set();
    let searchTimeout;

    // Initialize the application
    document.addEventListener('DOMContentLoaded', function() {
        initializeApp();
    });

    async function initializeApp() {
        setupEventListeners();
        await loadFiles();
        setupKeyboardShortcuts();
    }

    // Load files with error handling and loading states
    async function loadFiles() {
        showLoadingState();

        try {
            const response = await fetch('rapidgator_files.json');
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();
            files = data;
            filteredFiles = [...files];

            hideLoadingState();
            renderCurrentView();
            updateStats();
            showSuccessMessage(`Loaded ${files.length} files successfully`);

        } catch (error) {
            hideLoadingState();
            showErrorMessage(`Failed to load files: ${error.message}`);
            console.error('Error loading files:', error);
        }
    }

    // Enhanced filtering with multiple criteria
    function applyFilters() {
        const searchTerm = document.getElementById('search').value.toLowerCase();
        const sizeFilter = document.getElementById('sizeFilter').value;
        const typeFilter = document.getElementById('typeFilter').value;

        filteredFiles = files.filter(file => {
            // Search filter
            const matchesSearch = file.name.toLowerCase().includes(searchTerm);

            // Size filter
            const matchesSize = !sizeFilter || checkSizeFilter(file.size, sizeFilter);

            // Type filter
            const matchesType = !typeFilter || checkTypeFilter(file.name, typeFilter);

            return matchesSearch && matchesSize && matchesType;
        });

        // Apply sorting
        applySorting();

        // Reset to first page
        currentPage = 1;

        // Clear selection when filters change
        selectedFiles.clear();
        updateBulkActions();

        renderCurrentView();
        updateStats();
    }

    function checkSizeFilter(sizeStr, filter) {
        const sizeBytes = parseSize(sizeStr);
        const mb = 1024 * 1024;
        const gb = 1024 * mb;

        switch (filter) {
            case 'small': return sizeBytes < 100 * mb;
            case 'medium': return sizeBytes >= 100 * mb && sizeBytes < gb;
            case 'large': return sizeBytes >= gb && sizeBytes < 5 * gb;
            case 'xlarge': return sizeBytes >= 5 * gb;
            default: return true;
        }
    }

    function checkTypeFilter(filename, filter) {
        const ext = getFileExtension(filename).toLowerCase();
        const typeMap = {
            video: ['mp4', 'mkv', 'avi', 'mov', 'wmv', 'flv', 'webm', 'm4v'],
            archive: ['zip', 'rar', '7z', 'tar', 'gz', 'bz2', 'xz'],
            document: ['pdf', 'doc', 'docx', 'txt', 'rtf', 'odt'],
            image: ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'svg', 'webp'],
            audio: ['mp3', 'wav', 'flac', 'aac', 'ogg', 'm4a']
        };

        return typeMap[filter]?.includes(ext) || false;
    }

    function applySorting() {
        filteredFiles.sort((a, b) => {
            let valA = a[sortColumn] || '';
            let valB = b[sortColumn] || '';

            if (sortColumn === 'size') {
                const comparison = parseSize(valA) - parseSize(valB);
                return comparison * (sortDirection === 'asc' ? 1 : -1);
            } else {
                const comparison = valA.localeCompare(valB, undefined, { numeric: true });
                return comparison * (sortDirection === 'asc' ? 1 : -1);
            }
        });
    }

    // Rendering functions
    function renderCurrentView() {
        if (currentView === 'table') {
            renderTable();
        } else {
            renderCards();
        }
        renderPagination();
    }

    function renderTable() {
        const totalPages = Math.ceil(filteredFiles.length / pageSize) || 1;
        if (currentPage > totalPages) currentPage = totalPages;

        const start = (currentPage - 1) * pageSize;
        const end = start + pageSize;
        const pageData = filteredFiles.slice(start, end);

        const tbody = document.getElementById('tableBody');
        tbody.innerHTML = '';

        pageData.forEach((file, index) => {
            const globalIndex = start + index;
            const row = createTableRow(file, globalIndex);
            tbody.appendChild(row);
        });

        updateSelectAllCheckbox();
    }

    function createTableRow(file, index) {
        const row = document.createElement('tr');
        row.className = selectedFiles.has(index) ? 'selected' : '';
        row.setAttribute('data-index', index);

        row.innerHTML = `
            <td class="checkbox-column">
                <input
                    type="checkbox"
                    ${selectedFiles.has(index) ? 'checked' : ''}
                    onchange="toggleFileSelection(${index})"
                    aria-label="Select ${escapeHtml(file.name)}"
                >
            </td>
            <td class="icon-column">
                <span class="file-icon">${getFileIcon(file.name)}</span>
            </td>
            <td class="file-name">${escapeHtml(file.name)}</td>
            <td class="file-size">${escapeHtml(file.size)}</td>
            <td class="actions-column">
                <a href="${escapeHtml(file.url)}" target="_blank" class="file-link">
                    <svg width="14" height="14" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M14 3v2h3.59l-9.83 9.83 1.41 1.41L19 6.41V10h2V3h-7z"/>
                        <path d="M19 19H5V5h7V3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2v-7h-2v7z"/>
                    </svg>
                    Download
                </a>
            </td>
        `;

        return row;
    }

    function renderCards() {
        const totalPages = Math.ceil(filteredFiles.length / pageSize) || 1;
        if (currentPage > totalPages) currentPage = totalPages;

        const start = (currentPage - 1) * pageSize;
        const end = start + pageSize;
        const pageData = filteredFiles.slice(start, end);

        const container = document.getElementById('cardsContainer');
        container.innerHTML = '';

        pageData.forEach((file, index) => {
            const globalIndex = start + index;
            const card = createFileCard(file, globalIndex);
            container.appendChild(card);
        });
    }

    function createFileCard(file, index) {
        const card = document.createElement('div');
        card.className = `file-card ${selectedFiles.has(index) ? 'selected' : ''}`;
        card.setAttribute('data-index', index);
        card.onclick = () => toggleFileSelection(index);

        card.innerHTML = `
            <div class="card-header">
                <span class="card-icon">${getFileIcon(file.name)}</span>
                <div class="card-info">
                    <h3 class="card-name">${escapeHtml(file.name)}</h3>
                    <p class="card-size">${escapeHtml(file.size)}</p>
                </div>
                <input
                    type="checkbox"
                    class="card-checkbox"
                    ${selectedFiles.has(index) ? 'checked' : ''}
                    onclick="event.stopPropagation()"
                    onchange="toggleFileSelection(${index})"
                    aria-label="Select ${escapeHtml(file.name)}"
                >
            </div>
            <div class="card-actions">
                <a href="${escapeHtml(file.url)}" target="_blank" class="file-link">
                    <svg width="14" height="14" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M14 3v2h3.59l-9.83 9.83 1.41 1.41L19 6.41V10h2V3h-7z"/>
                        <path d="M19 19H5V5h7V3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2v-7h-2v7z"/>
                    </svg>
                    Download
                </a>
            </div>
        `;

        return card;
    }

    function renderPagination() {
        const totalPages = Math.ceil(filteredFiles.length / pageSize) || 1;
        const pagination = document.getElementById('pagination');

        let paginationHTML = '';

        // First page button
        paginationHTML += `
            <button
                class="page-btn"
                ${currentPage === 1 ? 'disabled' : ''}
                onclick="goToPage(1)"
                aria-label="Go to first page"
                title="First page"
            >⏮️</button>
        `;

        // Previous page button
        paginationHTML += `
            <button
                class="page-btn"
                ${currentPage === 1 ? 'disabled' : ''}
                onclick="goToPage(${currentPage - 1})"
                aria-label="Go to previous page"
                title="Previous page"
            >⬅️</button>
        `;

        // Page numbers
        const startPage = Math.max(1, currentPage - 2);
        const endPage = Math.min(totalPages, currentPage + 2);

        if (startPage > 1) {
            paginationHTML += `<button class="page-btn" onclick="goToPage(1)">1</button>`;
            if (startPage > 2) {
                paginationHTML += `<span class="page-ellipsis">...</span>`;
            }
        }

        for (let i = startPage; i <= endPage; i++) {
            paginationHTML += `
                <button
                    class="page-btn ${i === currentPage ? 'active' : ''}"
                    onclick="goToPage(${i})"
                    aria-label="Go to page ${i}"
                    ${i === currentPage ? 'aria-current="page"' : ''}
                >${i}</button>
            `;
        }

        if (endPage < totalPages) {
            if (endPage < totalPages - 1) {
                paginationHTML += `<span class="page-ellipsis">...</span>`;
            }
            paginationHTML += `<button class="page-btn" onclick="goToPage(${totalPages})">${totalPages}</button>`;
        }

        // Next page button
        paginationHTML += `
            <button
                class="page-btn"
                ${currentPage === totalPages ? 'disabled' : ''}
                onclick="goToPage(${currentPage + 1})"
                aria-label="Go to next page"
                title="Next page"
            >➡️</button>
        `;

        // Last page button
        paginationHTML += `
            <button
                class="page-btn"
                ${currentPage === totalPages ? 'disabled' : ''}
                onclick="goToPage(${totalPages})"
                aria-label="Go to last page"
                title="Last page"
            >⏭️</button>
        `;

        // Page info with jump input
        paginationHTML += `
            <div class="page-info">
                <input
                    type="number"
                    class="page-jump"
                    min="1"
                    max="${totalPages}"
                    value="${currentPage}"
                    onchange="goToPage(parseInt(this.value))"
                    aria-label="Jump to page"
                    title="Jump to page"
                >
                of ${totalPages}
            </div>
        `;

        pagination.innerHTML = paginationHTML;
    }

    // Utility functions
    function parseSize(sizeStr) {
        if (!sizeStr) return 0;
        const match = sizeStr.match(/([\d.]+)\s*(B|KB|MB|GB|TB)/i);
        if (!match) return 0;
        const num = parseFloat(match[1]);
        const unit = match[2].toUpperCase();
        const multipliers = {B:1, KB:1024, MB:1048576, GB:1073741824, TB:1099511627776};
        return num * (multipliers[unit] || 1);
    }

    function escapeHtml(text) {
        if (!text) return '';
        return text.replace(/[&<>"']/g, function(m) {
            return ({'&':'&amp;','<':'&lt;','>':'&gt;','"':'&quot;','\'':'&#39;'})[m];
        });
    }

    function getFileExtension(filename) {
        return filename.split('.').pop() || '';
    }

    function getFileIcon(filename) {
        const ext = getFileExtension(filename).toLowerCase();
        const iconMap = {
            // Video files
            'mp4': '🎬', 'mkv': '🎬', 'avi': '🎬', 'mov': '🎬', 'wmv': '🎬', 'flv': '🎬', 'webm': '🎬', 'm4v': '🎬',
            // Archive files
            'zip': '📦', 'rar': '📦', '7z': '📦', 'tar': '📦', 'gz': '📦', 'bz2': '📦', 'xz': '📦',
            // Document files
            'pdf': '📄', 'doc': '📄', 'docx': '📄', 'txt': '📄', 'rtf': '📄', 'odt': '📄',
            // Image files
            'jpg': '🖼️', 'jpeg': '🖼️', 'png': '🖼️', 'gif': '🖼️', 'bmp': '🖼️', 'svg': '🖼️', 'webp': '🖼️',
            // Audio files
            'mp3': '🎵', 'wav': '🎵', 'flac': '🎵', 'aac': '🎵', 'ogg': '🎵', 'm4a': '🎵'
        };
        return iconMap[ext] || '📁';
    }

    // Event handlers
    function setupEventListeners() {
        // Search with debouncing
        document.getElementById('search').addEventListener('input', debouncedSearch);

        // Filter selects
        document.getElementById('sizeFilter').addEventListener('change', applyFilters);
        document.getElementById('typeFilter').addEventListener('change', applyFilters);

        // Page size selector
        document.getElementById('pageSize').addEventListener('change', function() {
            pageSize = parseInt(this.value);
            currentPage = 1;
            renderCurrentView();
            updateStats();
        });

        // View toggle buttons
        document.getElementById('tableView').addEventListener('click', () => switchView('table'));
        document.getElementById('cardView').addEventListener('click', () => switchView('card'));

        // Select all checkbox
        document.getElementById('selectAll').addEventListener('change', toggleSelectAll);

        // Bulk action buttons
        document.getElementById('copyUrls').addEventListener('click', copySelectedUrls);
        document.getElementById('exportSelected').addEventListener('click', exportSelected);
        document.getElementById('clearSelection').addEventListener('click', clearSelection);

        // Sorting
        setupSortingListeners();
    }

    function debouncedSearch() {
        clearTimeout(searchTimeout);
        searchTimeout = setTimeout(() => {
            applyFilters();
        }, 300);
    }

    function setupSortingListeners() {
        document.querySelectorAll('#filesTable th[data-sort]').forEach(th => {
            th.addEventListener('click', function() {
                const col = th.getAttribute('data-sort');
                if (sortColumn === col) {
                    sortDirection = sortDirection === 'asc' ? 'desc' : 'asc';
                } else {
                    sortColumn = col;
                    sortDirection = 'asc';
                }

                // Update aria-sort attributes
                document.querySelectorAll('#filesTable th[data-sort]').forEach(header => {
                    header.removeAttribute('aria-sort');
                });
                th.setAttribute('aria-sort', sortDirection === 'asc' ? 'ascending' : 'descending');

                // Update sort indicators
                document.querySelectorAll('.sort-indicator').forEach(indicator => {
                    indicator.textContent = '';
                });
                th.querySelector('.sort-indicator').textContent = sortDirection === 'asc' ? '↑' : '↓';

                currentPage = 1;
                applyFilters();
            });

            // Make sortable headers keyboard accessible
            th.addEventListener('keydown', function(e) {
                if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    th.click();
                }
            });
        });
    }

    function setupKeyboardShortcuts() {
        document.addEventListener('keydown', function(e) {
            // Ctrl/Cmd + F for search focus
            if ((e.ctrlKey || e.metaKey) && e.key === 'f') {
                e.preventDefault();
                document.getElementById('search').focus();
                return;
            }

            // Don't handle shortcuts when typing in inputs
            if (e.target.tagName === 'INPUT' || e.target.tagName === 'SELECT') {
                // Escape to clear search
                if (e.key === 'Escape' && e.target.id === 'search') {
                    e.target.value = '';
                    applyFilters();
                    e.target.blur();
                }
                return;
            }

            // Arrow keys for pagination
            if (e.key === 'ArrowLeft' && currentPage > 1) {
                e.preventDefault();
                goToPage(currentPage - 1);
            } else if (e.key === 'ArrowRight') {
                e.preventDefault();
                const totalPages = Math.ceil(filteredFiles.length / pageSize) || 1;
                if (currentPage < totalPages) {
                    goToPage(currentPage + 1);
                }
            }

            // Ctrl/Cmd + A to select all
            if ((e.ctrlKey || e.metaKey) && e.key === 'a') {
                e.preventDefault();
                selectAllFiles();
            }

            // Delete key to clear selection
            if (e.key === 'Delete' || e.key === 'Backspace') {
                e.preventDefault();
                clearSelection();
            }

            // V key to toggle view
            if (e.key === 'v' || e.key === 'V') {
                e.preventDefault();
                switchView(currentView === 'table' ? 'card' : 'table');
            }
        });
    }

    // Navigation functions
    function goToPage(page) {
        const totalPages = Math.ceil(filteredFiles.length / pageSize) || 1;
        if (page >= 1 && page <= totalPages) {
            currentPage = page;
            renderCurrentView();
        }
    }

    function switchView(view) {
        currentView = view;

        // Update button states
        document.getElementById('tableView').classList.toggle('active', view === 'table');
        document.getElementById('cardView').classList.toggle('active', view === 'card');
        document.getElementById('tableView').setAttribute('aria-pressed', view === 'table');
        document.getElementById('cardView').setAttribute('aria-pressed', view === 'card');

        // Show/hide sections
        document.getElementById('tableSection').style.display = view === 'table' ? 'block' : 'none';
        document.getElementById('cardSection').style.display = view === 'card' ? 'block' : 'none';

        renderCurrentView();
    }

    // Selection functions
    function toggleFileSelection(index) {
        if (selectedFiles.has(index)) {
            selectedFiles.delete(index);
        } else {
            selectedFiles.add(index);
        }

        updateRowSelection(index);
        updateSelectAllCheckbox();
        updateBulkActions();
    }

    function updateRowSelection(index) {
        // Update table row
        const tableRow = document.querySelector(`#filesTable tr[data-index="${index}"]`);
        if (tableRow) {
            tableRow.classList.toggle('selected', selectedFiles.has(index));
            const checkbox = tableRow.querySelector('input[type="checkbox"]');
            if (checkbox) checkbox.checked = selectedFiles.has(index);
        }

        // Update card
        const card = document.querySelector(`#cardsContainer .file-card[data-index="${index}"]`);
        if (card) {
            card.classList.toggle('selected', selectedFiles.has(index));
            const checkbox = card.querySelector('input[type="checkbox"]');
            if (checkbox) checkbox.checked = selectedFiles.has(index);
        }
    }

    function toggleSelectAll() {
        const selectAllCheckbox = document.getElementById('selectAll');
        const start = (currentPage - 1) * pageSize;
        const end = Math.min(start + pageSize, filteredFiles.length);

        if (selectAllCheckbox.checked) {
            // Select all visible files
            for (let i = start; i < end; i++) {
                selectedFiles.add(i);
                updateRowSelection(i);
            }
        } else {
            // Deselect all visible files
            for (let i = start; i < end; i++) {
                selectedFiles.delete(i);
                updateRowSelection(i);
            }
        }

        updateBulkActions();
    }

    function selectAllFiles() {
        for (let i = 0; i < filteredFiles.length; i++) {
            selectedFiles.add(i);
        }
        renderCurrentView();
        updateSelectAllCheckbox();
        updateBulkActions();
    }

    function clearSelection() {
        selectedFiles.clear();
        renderCurrentView();
        updateSelectAllCheckbox();
        updateBulkActions();
    }

    function updateSelectAllCheckbox() {
        const selectAllCheckbox = document.getElementById('selectAll');
        const start = (currentPage - 1) * pageSize;
        const end = Math.min(start + pageSize, filteredFiles.length);

        let selectedCount = 0;
        for (let i = start; i < end; i++) {
            if (selectedFiles.has(i)) selectedCount++;
        }

        const totalVisible = end - start;
        selectAllCheckbox.checked = selectedCount === totalVisible && totalVisible > 0;
        selectAllCheckbox.indeterminate = selectedCount > 0 && selectedCount < totalVisible;
    }

    // Bulk actions
    function updateBulkActions() {
        const bulkActions = document.getElementById('bulkActions');
        const selectionCount = document.getElementById('selectionCount');

        if (selectedFiles.size > 0) {
            bulkActions.style.display = 'block';
            selectionCount.textContent = `${selectedFiles.size} file${selectedFiles.size === 1 ? '' : 's'} selected`;
        } else {
            bulkActions.style.display = 'none';
        }
    }

    async function copySelectedUrls() {
        const urls = Array.from(selectedFiles).map(index => filteredFiles[index].url);

        try {
            await navigator.clipboard.writeText(urls.join('\n'));
            showSuccessMessage(`Copied ${urls.length} URLs to clipboard`);
        } catch (error) {
            // Fallback for older browsers
            const textArea = document.createElement('textarea');
            textArea.value = urls.join('\n');
            document.body.appendChild(textArea);
            textArea.select();
            document.execCommand('copy');
            document.body.removeChild(textArea);
            showSuccessMessage(`Copied ${urls.length} URLs to clipboard`);
        }
    }

    function exportSelected() {
        const selectedData = Array.from(selectedFiles).map(index => filteredFiles[index]);
        const dataStr = JSON.stringify(selectedData, null, 2);
        const dataBlob = new Blob([dataStr], {type: 'application/json'});

        const link = document.createElement('a');
        link.href = URL.createObjectURL(dataBlob);
        link.download = `selected_files_${new Date().toISOString().split('T')[0]}.json`;
        link.click();

        showSuccessMessage(`Exported ${selectedData.length} files`);
    }

    // UI state functions
    function showLoadingState() {
        document.getElementById('loadingContainer').style.display = 'block';
        document.getElementById('tableSection').style.opacity = '0.5';
        document.getElementById('cardSection').style.opacity = '0.5';
    }

    function hideLoadingState() {
        document.getElementById('loadingContainer').style.display = 'none';
        document.getElementById('tableSection').style.opacity = '1';
        document.getElementById('cardSection').style.opacity = '1';
    }

    function showSuccessMessage(message) {
        const messageEl = document.getElementById('successMessage');
        messageEl.textContent = message;
        messageEl.style.display = 'block';
        setTimeout(() => {
            messageEl.style.display = 'none';
        }, 3000);
    }

    function showErrorMessage(message) {
        const messageEl = document.getElementById('errorMessage');
        messageEl.textContent = message;
        messageEl.style.display = 'block';
        setTimeout(() => {
            messageEl.style.display = 'none';
        }, 5000);
    }

    function updateStats() {
        const totalFiles = files.length;
        const filteredCount = filteredFiles.length;
        const searchTerm = document.getElementById('search').value;
        const sizeFilter = document.getElementById('sizeFilter').value;
        const typeFilter = document.getElementById('typeFilter').value;

        let statsText = `${filteredCount.toLocaleString()} of ${totalFiles.toLocaleString()} files`;

        if (searchTerm || sizeFilter || typeFilter) {
            const filters = [];
            if (searchTerm) filters.push(`search: "${searchTerm}"`);
            if (sizeFilter) filters.push(`size: ${sizeFilter}`);
            if (typeFilter) filters.push(`type: ${typeFilter}`);
            statsText += ` (filtered by ${filters.join(', ')})`;
        }

        document.getElementById('searchStats').textContent = statsText;
    }

    // Performance optimization: Virtual scrolling for very large datasets
    class VirtualScrollManager {
        constructor(container, itemHeight = 50) {
            this.container = container;
            this.itemHeight = itemHeight;
            this.visibleItems = Math.ceil(container.clientHeight / itemHeight) + 5;
            this.scrollTop = 0;
            this.enabled = false;
        }

        enable(data) {
            if (data.length > 1000) { // Only enable for large datasets
                this.enabled = true;
                this.data = data;
                this.setupVirtualScrolling();
            }
        }

        disable() {
            this.enabled = false;
        }

        setupVirtualScrolling() {
            // Implementation would go here for very large datasets
            // For now, we'll use regular pagination which is sufficient
        }
    }

    // Initialize virtual scroll manager
    const virtualScroll = new VirtualScrollManager(document.querySelector('.table-container'));

    // Accessibility improvements
    function announceToScreenReader(message) {
        const announcement = document.createElement('div');
        announcement.setAttribute('aria-live', 'polite');
        announcement.setAttribute('aria-atomic', 'true');
        announcement.className = 'sr-only';
        announcement.textContent = message;

        document.body.appendChild(announcement);
        setTimeout(() => {
            document.body.removeChild(announcement);
        }, 1000);
    }

    // Performance monitoring
    function measurePerformance(operation, fn) {
        const start = performance.now();
        const result = fn();
        const end = performance.now();
        console.log(`${operation} took ${(end - start).toFixed(2)}ms`);
        return result;
    }

    // Error boundary for graceful error handling
    window.addEventListener('error', function(event) {
        console.error('Application error:', event.error);
        showErrorMessage('An unexpected error occurred. Please refresh the page.');
    });

    // Service worker registration for offline support (optional)
    if ('serviceWorker' in navigator) {
        window.addEventListener('load', function() {
            // Uncomment to enable service worker
            // navigator.serviceWorker.register('/sw.js');
        });
    }
    </script>
</body>
</html>
