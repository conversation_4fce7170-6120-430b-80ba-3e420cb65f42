import requests
import time
import json
from bs4 import BeautifulSoup

BASE_URL = "https://rapidgator.net/folder/7380162/4K.html"

# Scrape a single page and return list of files (name, url, size)
def scrape_page(page):
    url = f"{BASE_URL}?page={page}&sort=created"
    resp = requests.get(url)
    soup = BeautifulSoup(resp.text, "html.parser")
    files = []
    for tr in soup.find_all("tr", class_=["odd", "even"]):
        a = tr.find("a", href=True)
        if not a:
            continue
        name = a.text.strip()
        url = "https://rapidgator.net" + a["href"]
        tds = tr.find_all("td")
        size = tds[1].text.strip() if len(tds) > 1 else ""
        files.append({"name": name, "url": url, "size": size})
    return files

def scrape_multiple_pages(num_pages=73, delay=1):
    all_files = []
    for page in range(1, num_pages + 1):
        print(f"Scraping page {page}...")
        files = scrape_page(page)
        all_files.extend(files)
        time.sleep(delay)
    return all_files

def save_to_json(files, filename="rapidgator_files.json"):
    with open(filename, "w", encoding="utf-8") as f:
        json.dump(files, f, ensure_ascii=False, indent=2)
    print(f"Saved {len(files)} files to {filename}")

if __name__ == "__main__":
    files = scrape_multiple_pages(num_pages=76, delay=1)
    save_to_json(files)
