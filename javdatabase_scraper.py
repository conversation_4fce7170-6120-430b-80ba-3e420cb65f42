import json
import requests
from bs4 import BeautifulSoup
import time
import re
import os
import shutil

INPUT_JSON = "rapidgator_files.json"
OUTPUT_JSON = "rapidgator_files_enriched.json"
BACKUP_JSON = "rapidgator_files_enriched.json.bak"
BASE_URL = "https://www.javdatabase.com/movies/"

def extract_movie_id(filename):
    """Extracts a movie ID (e.g., 'sone-725') from a filename using regex."""
    # This regex looks for a pattern of letters, a hyphen, and numbers.
    # It's designed to find common JAV ID formats like 'sone-725'.
    match = re.search(r'([a-zA-Z]+-\d+)', filename, re.IGNORECASE)
    if match:
        return match.group(1).lower()
    return None

def scrape_movie_details(movie_id):
    """Scrapes a JAVDatabase page for cover and gallery images."""
    if not movie_id:
        return None, None
    
    url = f"{BASE_URL}{movie_id}/"
    print(f"Scraping {url}...")
    
    try:
        response = requests.get(url, timeout=15)
        if response.status_code != 200:
            print(f"  -> Failed with status {response.status_code}")
            return None, None

        soup = BeautifulSoup(response.text, 'html.parser')
        
        # Find cover image
        cover_img_tag = soup.select_one('#poster-container img')
        cover_image = cover_img_tag['src'] if cover_img_tag else None
        
        # Find gallery images from the 'data-image-href' attribute
        gallery_links = soup.select('.row.g-3 .col-4 a')
        gallery = [link['data-image-href'] for link in gallery_links if 'data-image-href' in link.attrs]
        
        print(f"  -> Found cover and {len(gallery)} gallery images.")
        return cover_image, gallery

    except requests.RequestException as e:
        print(f"  -> Error scraping {url}: {e}")
        return None, None

def main():
    # Use enriched file as input if it exists, otherwise use the original
    input_file = OUTPUT_JSON if os.path.exists(OUTPUT_JSON) else INPUT_JSON
    
    if not os.path.exists(input_file):
        print(f"Error: Input file '{input_file}' not found.")
        return
        
    print(f"Loading data from '{input_file}'...")
    with open(input_file, 'r', encoding='utf-8') as f:
        files = json.load(f)

    # Create a backup of the output file before starting
    if os.path.exists(OUTPUT_JSON):
        shutil.copy(OUTPUT_JSON, BACKUP_JSON)
        print(f"Backup of '{OUTPUT_JSON}' created at '{BACKUP_JSON}'")

    total = len(files)

    # Process each file
    for i, file_data in enumerate(files):
        # Check if data already exists to avoid re-scraping
        if file_data.get('cover_image') is not None:
            continue

        print(f"Processing file {i+1}/{total}: {file_data['name']}")
        
        movie_id = extract_movie_id(file_data['name'])
        if not movie_id:
            print("  -> Could not determine movie ID, skipping.")
            # Mark as processed to avoid trying again
            file_data['cover_image'] = None 
            file_data['gallery'] = []
        else:
            cover, gallery = scrape_movie_details(movie_id)
            file_data['movie_id'] = movie_id
            file_data['cover_image'] = cover
            file_data['gallery'] = gallery
        
        # Save the entire database after every update (real-time update)
        with open(OUTPUT_JSON, 'w', encoding='utf-8') as f:
            json.dump(files, f, ensure_ascii=False, indent=2)
        
        # Be polite to the server
        if movie_id:
            time.sleep(1)

    print(f"\nProcessing complete. Enriched data is in '{OUTPUT_JSON}'.")

if __name__ == "__main__":
    main()
